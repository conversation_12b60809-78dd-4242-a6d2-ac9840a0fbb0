# ProjectDigitizer WPF项目重构任务管理

## 📋 项目概述

**项目名称**: ProjectDigitizer WPF架构重构  
**项目目标**: 建立清晰的分层架构，提升代码质量和可维护性  
**预计工期**: 10-13周  
**负责人**: 开发团队  

## 🎯 重构目标

1. **消除代码重复**: 合并重复的类和枚举定义
2. **建立分层架构**: 实现Clean Architecture + DDD模式
3. **优化MVVM实现**: 分离UI逻辑和业务逻辑
4. **提升可维护性**: 建立清晰的依赖关系和服务边界

## 📊 任务状态说明

- `[ ]` **未开始** (NOT_STARTED): 任务尚未开始
- `[/]` **进行中** (IN_PROGRESS): 任务正在执行
- `[x]` **已完成** (COMPLETED): 任务已完成并通过验收
- `[-]` **已取消** (CANCELLED): 任务已取消或不再需要

## 🏗️ 重构阶段概览

### 阶段1: 基础设施重构 (1-2周)
**目标**: 建立清晰的项目结构和依赖关系

### 阶段2: 领域层重构 (2-3周)  
**目标**: 提取纯业务逻辑，建立领域模型

### 阶段3: 应用层重构 (2-3周)
**目标**: 实现CQRS模式，建立应用服务

### 阶段4: 表示层重构 (2-3周)
**目标**: 优化MVVM实现，重构UI逻辑

### 阶段5: 测试和优化 (1-2周)
**目标**: 完善测试覆盖，性能优化

---

## 🔧 阶段1: 基础设施重构

### 1.1 项目结构标准化

#### 1.1.1 重命名ProjectDigitizer.Studio项目
- **状态**: `[ ]` 未开始
- **负责人**: 待分配
- **预计工时**: 2小时
- **开始时间**: 待定
- **完成时间**: 待定

**任务描述**: 将ProjectDigitizer.Studio重命名为ProjectDigitizer.Presentation，体现其表示层职责

**涉及文件**:
- `ProjectDigitizer.Studio/ProjectDigitizer.Studio.csproj` → `ProjectDigitizer.Presentation/ProjectDigitizer.Presentation.csproj`
- `ProjectDigitizer.sln` (更新项目引用)
- 所有命名空间从 `ProjectDigitizer.Studio` 更新为 `ProjectDigitizer.Presentation`

**验收标准**:
- [ ] 项目成功重命名，无编译错误
- [ ] 所有命名空间引用正确更新
- [ ] 解决方案文件正确引用新项目名称
- [ ] Git历史保持完整

**风险评估**: 🟡 中等风险 - 可能影响现有引用关系
**回滚计划**: 保留重命名前的Git提交，可快速回滚

---

#### 1.1.2 创建ProjectDigitizer.Shared项目
- **状态**: `[ ]` 未开始
- **负责人**: 待分配
- **预计工时**: 3小时
- **开始时间**: 待定
- **完成时间**: 待定

**任务描述**: 创建共享项目，用于存放通用工具类、扩展方法和常量

**涉及文件**:
- 新建 `src/ProjectDigitizer.Shared/ProjectDigitizer.Shared.csproj`
- 新建 `src/ProjectDigitizer.Shared/Extensions/`
- 新建 `src/ProjectDigitizer.Shared/Utilities/`
- 新建 `src/ProjectDigitizer.Shared/Constants/`

**验收标准**:
- [ ] 项目创建成功，包含基本文件夹结构
- [ ] 项目正确添加到解决方案
- [ ] 其他项目可以正常引用Shared项目
- [ ] 包含基本的README.md说明文档

**依赖关系**: 无前置依赖
**风险评估**: 🟢 低风险
**回滚计划**: 删除新创建的项目文件

---

#### 1.1.3 重组文件夹结构
- **状态**: `[ ]` 未开始
- **负责人**: 待分配
- **预计工时**: 4小时
- **开始时间**: 待定
- **完成时间**: 待定

**任务描述**: 按照Clean Architecture原则重组项目文件夹结构

**涉及文件**:
- 移动 `ProjectDigitizer.Studio/` → `src/ProjectDigitizer.Presentation/`
- 移动 `ProjectDigitizer.Application/` → `src/ProjectDigitizer.Application/`
- 移动 `ProjectDigitizer.Core/` → `src/ProjectDigitizer.Core/`
- 移动 `ProjectDigitizer.Infrastructure/` → `src/ProjectDigitizer.Infrastructure/`

**验收标准**:
- [ ] 所有项目移动到src文件夹下
- [ ] 项目引用路径正确更新
- [ ] 编译和运行正常
- [ ] 测试项目路径正确更新

**依赖关系**: 依赖于1.1.1和1.1.2完成
**风险评估**: 🟡 中等风险 - 可能影响构建脚本和CI/CD
**回滚计划**: 使用Git恢复原始文件夹结构

---

### 1.2 依赖注入重构

#### 1.2.1 重构MainWindow.xaml.cs的服务实例化
- **状态**: `[ ]` 未开始
- **负责人**: 待分配
- **预计工时**: 4小时
- **开始时间**: 待定
- **完成时间**: 待定

**任务描述**: 移除MainWindow中的直接服务实例化，改为通过依赖注入获取

**涉及文件**:
- `src/ProjectDigitizer.Presentation/MainWindow.xaml.cs`
- `src/ProjectDigitizer.Presentation/App.xaml.cs`

**当前问题**:
```csharp
// 当前代码 - 直接实例化
_projectFileService = new ProjectFileService();
_canvasViewModel = new CanvasViewModel();
_propertyPanelManager = new PropertyPanelManager(PropertyPanelContainer);
```

**目标代码**:
```csharp
// 重构后 - 依赖注入
public MainWindow(IProjectFileService projectFileService, 
                  CanvasViewModel canvasViewModel,
                  IPropertyPanelManager propertyPanelManager)
{
    _projectFileService = projectFileService;
    _canvasViewModel = canvasViewModel;
    _propertyPanelManager = propertyPanelManager;
}
```

**验收标准**:
- [ ] MainWindow构造函数接受依赖注入参数
- [ ] 移除所有直接的new操作符实例化
- [ ] 服务在DI容器中正确注册
- [ ] 应用程序正常启动和运行

**依赖关系**: 需要先完成服务接口定义
**风险评估**: 🟡 中等风险 - 可能影响应用启动
**回滚计划**: 保留原始实例化代码作为备份

---

#### 1.2.2 建立统一的DI容器配置
- **状态**: `[ ]` 未开始
- **负责人**: 待分配
- **预计工时**: 6小时
- **开始时间**: 待定
- **完成时间**: 待定

**任务描述**: 在App.xaml.cs中建立完整的依赖注入配置

**涉及文件**:
- `src/ProjectDigitizer.Presentation/App.xaml.cs`
- `src/ProjectDigitizer.Application/DependencyInjection/ServiceCollectionExtensions.cs`
- 新建 `src/ProjectDigitizer.Presentation/DependencyInjection/PresentationServiceExtensions.cs`

**验收标准**:
- [ ] 所有服务在启动时正确注册
- [ ] 服务生命周期正确配置(Singleton/Scoped/Transient)
- [ ] 接口和实现正确映射
- [ ] 循环依赖检查通过

**依赖关系**: 依赖于1.2.1完成
**风险评估**: 🟡 中等风险
**回滚计划**: 保留原始App.xaml.cs配置

---

### 1.3 代码去重和整合

#### 1.3.1 合并重复的ViewModelBase类
- **状态**: `[ ]` 未开始
- **负责人**: 待分配
- **预计工时**: 3小时
- **开始时间**: 待定
- **完成时间**: 待定

**任务描述**: 合并Application层和Presentation层的ViewModelBase类

**涉及文件**:
- `src/ProjectDigitizer.Application/ViewModels/ViewModelBase.cs` (保留)
- `src/ProjectDigitizer.Presentation/ViewModels/ViewModelBase.cs` (删除)
- 更新所有继承ViewModelBase的类的命名空间引用

**当前问题**: 存在两个功能相似但实现不同的ViewModelBase类

**验收标准**:
- [ ] 只保留一个ViewModelBase类在Application层
- [ ] 所有ViewModel正确继承统一的基类
- [ ] 功能完整性保持不变
- [ ] 编译无错误，运行正常

**依赖关系**: 无前置依赖
**风险评估**: 🟢 低风险
**回滚计划**: 保留删除前的文件备份

---

#### 1.3.2 统一ModuleType枚举定义
- **状态**: `[ ]` 未开始
- **负责人**: 待分配
- **预计工时**: 2小时
- **开始时间**: 待定
- **完成时间**: 待定

**任务描述**: 移除重复的ModuleType枚举，统一使用Core层定义

**涉及文件**:
- `src/ProjectDigitizer.Core/Enums/ModuleType.cs` (保留)
- `src/ProjectDigitizer.Presentation/Models/ModuleModel.cs` (移除枚举定义)
- 更新所有使用ModuleType的文件的命名空间引用

**验收标准**:
- [ ] 只保留Core层的ModuleType枚举
- [ ] 所有引用正确更新到Core层
- [ ] 枚举值完整性保持不变
- [ ] 编译和运行正常

**依赖关系**: 无前置依赖
**风险评估**: 🟢 低风险
**回滚计划**: 保留原始枚举定义

---

#### 1.3.3 提取公共工具类到Shared项目
- **状态**: `[ ]` 未开始
- **负责人**: 待分配
- **预计工时**: 5小时
- **开始时间**: 待定
- **完成时间**: 待定

**任务描述**: 识别并移动公共工具类到Shared项目

**涉及文件**:
- 移动 `src/ProjectDigitizer.Presentation/Controls/BasicMathFunctions.cs` → `src/ProjectDigitizer.Shared/Utilities/`
- 移动 `src/ProjectDigitizer.Presentation/Controls/ExtendedMathFunctions.cs` → `src/ProjectDigitizer.Shared/Utilities/`
- 移动 `src/ProjectDigitizer.Presentation/Controls/StringFunctions.cs` → `src/ProjectDigitizer.Shared/Utilities/`
- 移动 `src/ProjectDigitizer.Presentation/Controls/LogicalFunctions.cs` → `src/ProjectDigitizer.Shared/Utilities/`

**验收标准**:
- [ ] 工具类成功移动到Shared项目
- [ ] 命名空间正确更新
- [ ] 所有引用正确更新
- [ ] 功能测试通过

**依赖关系**: 依赖于1.1.2完成
**风险评估**: 🟡 中等风险 - 可能影响现有功能
**回滚计划**: 保留原始文件位置

---

### 1.4 基础设施建立

#### 1.4.1 统一日志记录机制
- **状态**: `[ ]` 未开始
- **负责人**: 待分配
- **预计工时**: 4小时
- **开始时间**: 待定
- **完成时间**: 待定

**任务描述**: 建立基于Serilog的统一日志记录系统

**涉及文件**:
- 新建 `src/ProjectDigitizer.Shared/Logging/LoggerConfiguration.cs`
- 更新 `src/ProjectDigitizer.Presentation/App.xaml.cs`
- 更新 `src/ProjectDigitizer.Application/DependencyInjection/ServiceCollectionExtensions.cs`

**验收标准**:
- [ ] Serilog正确配置和初始化
- [ ] 支持文件和控制台输出
- [ ] 日志级别可配置
- [ ] 所有层都可以使用ILogger接口

**依赖关系**: 无前置依赖
**风险评估**: 🟢 低风险
**回滚计划**: 移除Serilog配置，恢复原始日志

---

## 📈 进度跟踪

### 总体进度
- **阶段1**: 0% (0/12 任务完成)
- **阶段2**: 0% (0/8 任务完成)  
- **阶段3**: 0% (0/10 任务完成)
- **阶段4**: 0% (0/9 任务完成)
- **阶段5**: 0% (0/6 任务完成)

### 风险监控
- 🔴 **高风险任务**: 0个
- 🟡 **中等风险任务**: 6个
- 🟢 **低风险任务**: 6个

---

## 📝 变更日志

| 日期 | 变更内容 | 负责人 | 备注 |
|------|----------|--------|------|
| 2025-01-XX | 创建重构任务管理文档 | 系统 | 初始版本 |

---

## 🔗 相关文档

- [重构进度报告](./重构进度报告.md)
- [架构设计文档](./架构设计.md)
- [开发文档](../Doc/02_开发文档.md)
